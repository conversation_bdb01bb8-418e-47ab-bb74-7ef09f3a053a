---
import contact from '@/lib/json/contact.json'
---

<footer class="w-full mx-auto lg:p-10 p-5 bg-muted/20 border-t">
    <div class="flex flex-col lg:flex-row gap-4 items-center justify-between w-full max-w-7xl mx-auto">
        <div>
            <p>&copy; {new Date().getFullYear()} <PERSON>. All rights reserved.</p>
        </div>
        <div>
            <ul class="flex space-x-4">
                {
                    ['github', 'Instagram', 'devto'].map((link) => (
                        <li>
                            <a href={contact[link].name} class="hover:text-primary transition duration-200">
                                <img src={`/contact/${contact[link].icon}.svg`} alt={contact[link].title} class="h-5 w-5" />
                            </a>
                        </li>
                    ))
                }
            </ul>
        </div>
    </div>
</footer>
