---
import { epicProjects, projects } from '@/lib/data'
import ProjectDetails from '@/components/ProjectDetails'
---

<div class="w-full max-w-3xl mx-auto mt-14 lg:mt-20 mb-10 grid gap-3 grid-cols-1 lg:grid-cols-2">
    <div class="col-span-full w-full items-center justify-center flex-col mx-auto max-w-2xl lg:text-center mb-2">
        <h1 class="text-lg lg:text-3xl font-bold w-full">Check out my latest work</h1>
        <p class="text-foreground/60 hidden lg:flex">I've worked on a variety of projects, from simple websites to complex web applications. Here are a few of my favorites.</p>
    </div>
    {
        epicProjects.slice(0, 6).map((v: any, index) => (
            <ProjectDetails
                data={
                    projects.filter((i) => {
                        return i.name === v.name
                    })[0]
                }
                client:load
            />
        ))
    }
</div>
