---
import { projects, newProjectSchemas } from '@/lib/data'
import Layout from '../../layout/layout.astro'
import ProjectDetails from '@/components/ProjectDetails'
---

<Layout>
    <div class="w-full max-w-5xl mx-auto ">
        {
            Object.keys(newProjectSchemas).map((family, index) => (
                <div class={"w-full max-w-4xl mx-auto lg:mt-20 mb-10 grid gap-3 grid-cols-1 md:grid-cols-2 " + (index !== 0 ? " mt-14" : "")}>
                    <div class="col-span-full w-full items-center justify-center flex-col mx-auto max-w-2xl lg:text-center mb-2">
                        <h1 class="text-lg lg:text-3xl font-bold  w-full ">{newProjectSchemas[family].title}</h1>
                        <p class="text-foreground/60 hidden lg:flex"> {newProjectSchemas[family].description}</p>
                    </div>
                    {newProjectSchemas[family].apps.map((project_, index) => {
                        const project = projects[project_]
                        return <ProjectDetails data={project} client:load />
                    })}
                </div>
            ))
        }
    </div>
</Layout>
