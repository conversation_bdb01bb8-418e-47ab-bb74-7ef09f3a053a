---
import Layout from "../../layout/layout.astro";
import stack from "@/lib/json/stack.json";
import { Button } from "@/components/ui/button";
---

<Layout>
    <div
        class="flex flex-col mx-auto w-full max-w-3xl lg:py-10 gap-10 lg:space-y-12"
    >
        {
            stack.map((val) => (
                <div class="gap-3 w-full grid md:grid-cols-3 grid-cols-2 lg:grid-cols-5 lg:px-5">
                    <div class="col-span-full">
                        <h1 class="font-medium !text-xl text-foreground/80">
                            {val.name}
                        </h1>
                    </div>

                    {val.children.map((childVal: any) => (
                        <div class="border bg-muted/20 backdrop-blur p-1 rounded-lg">
                            <div class="w-full relative h-20 overflow-hidden flex items-center justify-center">
                                <img
                                    src={childVal.svg}
                                    style={childVal?.style}
                                    class="h-16"
                                />
                            </div>
                            <div class="w-full flex items-center justify-center  mb-2 text-foreground/60 text-sm">
                                {childVal.name}
                            </div>
                        </div>
                    ))}
                </div>
            ))
        }
    </div>
    <div class="h-10 w-full"></div>
</Layout>
