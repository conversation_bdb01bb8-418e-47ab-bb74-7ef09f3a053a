---
import experience from '@/lib/json/experience.json'
---

<div class="w-full h-full flex flex-col">
    <div class="pt-5 px-5">
        <h1 class="text-lg lg:text-xl font-bold">Work Experience</h1>
    </div>
    <div class="w-full h-full p-5 space-y-3 bg-gradient-to-t from-muted/10 via-transparent">
        {
            experience.jobs.slice(0, 2).map((item, index) => (
                <div class="w-full flex justify-between items-end">
                    <div class="flex items-center justify-center gap-2">
                        <div class="w-12 h-12 bg-muted/20 border flex items-center justify-center rounded-full p-1.5">
                            <img class="filter invert w-full opacity-60" src={item.image} />
                        </div>
                        <div class="">
                            <p class="text-md text-foreground/90">
                                {item.company}
                                <span class="text-sm text-foreground/60"> {item.title} </span>
                            </p>
                            <p class="text-xs text-foreground/50">{item.date.split('(')[0]}</p>
                        </div>
                    </div>
                </div>
            ))
        }
    </div>
</div>
