---
import { projects } from '@/lib/data'
import { Button } from '@/components/ui/button'
import { useHalfText } from '@/lib/functions/useHelfText'
import { Icon } from 'astro-icon'
import { Compass, Sparkles } from 'lucide-react'
---

<div class="w-full mt-14 lg:mt-32">
    <div class="flex text-2xl space-y-1 font-medium tracking-wide items-center justify-center flex-col w-full mb-10 lg:mb-14">
        <div class="w-full gap-2 lg:gap-4 flex items-center justify-center text-lg lg:text-4xl text-foreground/60 font-semibold">
            <Sparkles size={40} className="hidden lg:block" />
            <Sparkles size={25} className="lg:hidden block" />
            Best Applications
        </div>
        <div class="flex h-5 overflow-visible items-center justify-start relative">
            <img src="/image/sin.svg" class="w-32 lg:w-60" />
            <img src="/image/sin.svg" class="blur-lg absolute w-32 lg:w-60" />
        </div>
    </div>
    <div class="hiddden mt-4 grid grid-cols-1 lg:grid-cols-3 gap-2">
        {
            projects.map(
                (p, index) =>
                    p.logo && (
                        <div class={`border `}>
                            <div class={'w-full mb-4 border-b overflow-hidden flex items-center justify-center '}>{p.images.map((i, index) => index === 0 && <img src={i.replace('upload', 'upload/w_500')} class="lg:hover:scale-110 ease-in-out duration-200 w-full bg-background h-40 object-left-top object-cover" />)}</div>
                            <div class="px-5 gap-1 flex w-max items-center justify-center">
                                <div class="h-10 w-10 flex items-center justify-center overflow-visible relative">
                                    <img src={`/svg/${p.logo}`} class={`${false ? 'h-7 -translate-y-0.5 ' : 'h-8'} `} />
                                    <img src={`/svg/${p.logo}`} class={`${false ? 'h-7 -translate-y-0.5 ' : 'h-8'} hidden lg:block absolute blur-xl `} />
                                </div>
                                <h1 class="font-bold mr-2 ml-1">{p.name}</h1>
                                <div class="max-w-full w-max flex flex-wrap gap-1">
                                    {p.tags
                                        .split(' ')
                                        .slice(0, 2)
                                        .map((t) => (
                                            <div class="w-max text-xs bg-muted px-2 py-0.5 rounded-full text-foreground/50 m-auto">{t}</div>
                                        ))}
                                </div>
                            </div>
                            <p class="mt-2 text-sm px-5 mb-3 font-light text-foreground/70">{useHalfText(p.description, 85)}</p>
                            <div class="w-full p-2 flex items-center justify-between gap-2">
                                <a href={`/project/${index}`} class="w-full">
                                    <Button variant="outline" className="overflow-hidden w-full gap-2 bg-muted border-none text-muted-foreground">
                                        <Compass size={18} />
                                        Explore
                                    </Button>
                                </a>
                            </div>
                        </div>
                    ),
            )
        }
    </div>
</div>
