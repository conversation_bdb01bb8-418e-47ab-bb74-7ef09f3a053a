@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: "Poppins", sans-serif;
  overflow: hidden;
}

* {
  scroll-behavior: smooth;
}

@layer base {
  :root {
    --background: 229 41% 4%;
    --foreground: 229 23% 99%;
    --muted: 229 12% 14%;
    --muted-foreground: 229 10% 63%;
    --popover: 229 41% 5%;
    --popover-foreground: 0 0% 100%;
    --card: 229 41% 5%;
    --card-foreground: 0 0% 100%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --primary: 210 98.39% 52.23%;
    --primary-foreground: 0 0% 100%;
    --secondary: 229 14% 8%;
    --secondary-foreground: 229 14% 68%;
    --accent: 229 23% 17%;
    --accent-foreground: 229 23% 77%;
    --destructive: 3 89% 54%;
    --destructive-foreground: 0 0% 100%;
    --ring: 229 100% 62%;
    --radius: 0.5rem;
  }
}

* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) transparent;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--primary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: color-mix(in srgb, hsl(var(--primary)) 80%, white);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

* {
  @apply !border-input;
}

.react-activity-calendar>* {
  @apply text-foreground/50;
}

.bg {
  position: fixed;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  width: 150%;
  height: 200vh;
  background: url(https://indieground.net/wp-content/uploads/2023/01/noise-01.png) repeat;
  background-repeat: repeat;
  opacity: .8;
  visibility: visible;
  pointer-events: none;
  display: block;
}