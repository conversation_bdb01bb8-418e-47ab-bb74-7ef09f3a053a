---
import Layout from '../../layout/layout.astro'
import contact from '../../lib/json/contact.json' 
---

<Layout>
    <div class="w-full mt-5  min-h-[65vh] lg:flex flex-col items-center justify-center">
        <h1 class="text-2xl font-bold mx-auto mb-2 w-max">Get in Touch 🔭</h1>
        <p class="text-center max-w-2xl mx-auto text- text-foreground/70">Have a question, feedback, or just want to say hello? We'd love to hear from you! Feel free to reach out using the contact information below, and we'll get back to you as soon as possible. 😎</p>
        <div class="mx-auto w-full lg:w-max mt-5 grid grid-cols-2 lg:grid-cols-3 gap-2">
            {
                Object.keys(contact).map((link) => (
                    <a href={contact[link].name} class="flex lg:hover:text-foreground ease-in-out duration-200 lg:hover:bg-muted/10 lg:hover:gap-4 gap-3 p-4 bg-muted/20 backdrop-blur rounded-lg border w-full text-sm lg:last:items-center lg:last:justify-center text-foreground/70 lg:last:col-span-2">
                        <img src={`/contact/${contact[link].icon}.svg`} height={20} width={20} />
                        {contact[link].title}
                    </a>
                ))
            }
        </div>
</Layout>
