---
import experience from '@/lib/json/experience.json'
---

<div class="w-full max-w-3xl mx-auto mt-14 lg:mt-24 grid gap-3">
    <div class="col-span-full w-full items-center justify-center flex-col mx-auto max-w-2xl lg:text-center mb-2">
        <h1 class="text-lg lg:text-3xl font-bold">{experience.title}</h1>
        <p class="text-foreground/60 max-w-xl hidden lg:flex">{experience.description}</p>
    </div>
    <div class="grid grid-cols-1 w-full mx-auto max-w-2xl">
        {
            experience.jobs.map((item, index) => (
                <div class={'w-full flex flex-col gap-1 justify-between py-2 lg:py-4 border-dashed' + ((index === 0) ? ' lg:border-t' : ' lg:border-t')}>
                    <div class="flex items-start justify-start gap-4">
                        <div class={'h-12 w-12 bg-muted/20 rounded-full flex items-center justify-center border p-1.5 '}>
                            <img class="filter invert w-full opacity-60" src={item.image} />
                        </div>
                        <div class="">
                            <div class="w-full">
                                <p class="text-xs text-foreground/50 whitespace-nowrap">{item.date}</p>
                                <div class="text-lg text-foreground/90 mb-2 flex flex-row gap-1 items-center">
                                    <div class="lg:hidden">{index === 0 ? "BPW" : item.company}</div>
                                    <div class="lg:flex hidden">{item.company}</div>
                                    <span class="text-sm text-foreground/60">{item.title}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="text-foreground/50 text-sm font-thin w-full lg:pl-16 hidden lg:flex">{item.description}</p>
                    {item?.tech && (
                        <div class="flex-wrap gap-2 w-full mt-2 lg:pl-16 hidden lg:flex">
                            {item.tech.map((tech) => (
                                <p class="text-primary text-xs font-thin  bg-primary/10 rounded-full px-3 py-1">{tech}</p>
                            ))}
                        </div>
                    )}
                </div>
            ))
        }
    </div>
</div>
