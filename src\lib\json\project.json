[{"name": "Eyebase", "logo": "6.png", "url": "https://eyebase.vercel.app", "tags": "next13 react postgresql shadcn", "description": "🚀 The first headless backend that lets you easily add and manage your data, connecting seamlessly with API .", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/hsqxkw8ayt0wbzjqqals.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/pywcjpzggychhseeg5hx.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/xfzb1jgype2cmkyt2tvx.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/a8flzi6rbmoewoe5f0yv.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/z1e0ukwvwlgehafs5hbi.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/mmvgjfgdymemuzqtb2kj.webp"]}, {"name": "My father website", "url": "https://bachir-art.vercel.app/", "tags": "astro react tailwind", "description": "🌟 A dedicated website for my father to showcase his works and products, along with additional information.", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/h97zsoz9tme5khtilgww.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/haqucxszfr3j29y4ktg2.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/dfgjhjyt2mea2pvbfouq.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1730583800/y1k78x9mihwmfkchacqz.webp"]}, {"name": "Agria group", "url": "https://agriagroup.tn/", "tags": "next13 react postgresql colify shadcn", "description": "🚀 Considered one of my best apps! Built from scratch: a complete CMS for a multi-language website with content management, member tracking, visitor analytics, and a visual editor.", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/re19ghceyhsjh3anvxmp.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154015/wtbz9szcodlvucmbnhgp.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154025/abmkfxxmcclzseijtnc4.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154028/yyrpequsomu88nzxid1e.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154016/f9xuv0fugpobqeoom6fi.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154014/vjtdylg7mnqwvh4vehjk.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153947/mzahax0cso64lqm1kker.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153993/wcg0q8w0vgvasrzcnn9b.webp"]}, {"name": "FGMC", "description": "🌟 Popular website in France, leading in e-commerce. Expertise in migrating from PrestaShop to Next.js for enhanced performance and scalability.", "tags": "next13 react tailwindcss prestashop", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153915/crfjigfnw8nvrwlwvw7k.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153897/u6n6jgq7nrp2ypa2ajdk.webp"]}, {"name": "Elboutic", "description": "🛍️ Popular Tunisian shopping app with a wide range of products. I redesigned the entire frontend and implemented a powerful admin panel for enhanced efficiency.", "tags": "next13 react express mongo mongoose shadcn", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153931/icw5mhdsonuwvc2wdd0z.webp"]}, {"name": "Etudi", "description": "📚 An app to find studies, create posts, and manage teaching. Includes a robust admin panel for complete control.", "tags": "next13 react postgresql shadcn", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154003/n0byqdnyo2nlctwfpf3n.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154004/wx4cfgucsjeepm9mwa8t.webp"]}, {"name": "Maison log", "tags": "next13 react postgresql shadcn", "description": "🏠 Maison Log is a simple app for posting and hosting rental listings without requiring authentication. It supports map filtering and was built in a single day.", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154017/ckejh9x2y1d9wnloobyv.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154008/q2utq3jpe9v62lxgqazm.webp"]}, {"name": "<PERSON>", "url": "https://cool.rjaziz.com", "tags": "framermotion react", "description": "🎨 Finally mastered Framer Motion! Created my new, elegantly animated portfolio inspired by old European art.", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139152/vamia5rwtqmzrbfevyf0.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139538/jdrpz4m9bbllzzxd7udd.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139762/flqmankmxmvop5td4ksi.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139453/aqjn292somjp09tdq3jx.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139522/wadxe6823yi32izd4nqq.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139489/tfsuv7tvdwjzrbbbcrzb.webp"]}, {"name": "Sparkels", "tags": "next13 react postgresql shadcn", "description": "🌍 The best SaaS I've built! It's a product and stock management system that supports over 10 languages, allowing you to control workers, sales, and POS operations.", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154010/fgzq5eu2j3osbo3to7p1.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153999/ocbemw3mvjdv3fzdbshl.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154043/wrwhdn3crkryy5xmehxw.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153994/xorcgkarnypk6ob0jwt8.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153992/thih5ejxebbqllqppouh.webp"]}, {"name": "Nsairi Events", "tags": "mobirise", "url": "https://nsairievents.rjaziz.com/", "description": "🌟 Designed a simple yet beautiful static website for RBVRNT using Mobirise, without writing a single line of code—my first experience with web design!", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154042/l9js9o1kwtnepbqnrae8.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721154002/db7h7s9vnm1ndkz2eilv.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153993/xejnq9tjpjdlookxa79q.webp"]}, {"name": "J<PERSON><PERSON> portfolio", "description": "📸 Simple portfolio showcasing the photography services and works of a talented photographer.", "tags": "next13 react postgresql shadcn", "url": "https://jasserhmissi.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1721156295/gu2lf4puheujjpellwrq.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721156429/u9l6p987bq9swnlz7u8b.webp"]}, {"logo": "4.svg", "name": "Type AI", "description": "Seamlessly integrate AI with personalized templates, cluster management, and secure API key generation. 🚀 Revolutionize your workflow effortlessly!", "tags": "next13 react postgresql zod mistar-ai huggingface ai", "url": "https://ai-type.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/g4ry1fcdksl7dm7urhji.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721139578/wcmalawmnmsopcjt5okf.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713216265/iuesp7ulxgcgbgcu0sdo.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713216263/bhoqzw2x9hd7fgtigife.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713216262/rtase1fexqvoo8jfafnd.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713216258/loi4mxfrwlzt9obar06v.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713216260/rpr3lepflb130x4qrejt.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713216262/xpgwifw4gtveiuzkwn3z.webp"], "md": 11}, {"logo": "2.svg", "name": "Light Ai 2.0", "description": "🚀 Unleash your creativity with our free text-to-image web app! 💬✨ Join 2500+ users and explore 20,000+ images. Exciting new features include reacting to others' creations and improved performance. Transform words into visuals effortlessly – join for free on our website! 🎨🌟 #TextToImage #CreativeFreedom", "tags": "next13 react firebase huggingface ai", "url": "https://light-ai.vercel.app", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/xsd1lzsrxyyatf2ndj5c.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378898/portfolio/khiiu5v8r6sukrka2duv.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378898/portfolio/ydpfy7izfpllnfnnvwv3.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378897/portfolio/zmddv7efhcdtlaer5d6g.webp"], "md": 2}, {"logo": "1.svg", "name": "Neoflow", "description": "🌐 Dive into diagram magic with Neoflow! 🚀✨ Powered by AI, it effortlessly creates stunning class, flow, and sequence diagrams. Plus, enjoy team management and gorgeous design! 💻📊 #Neoflow #DiagramCreation ", "tags": "next13 prisma shadcn CockroachDB llama mermaidJs firework_ai excalidraw", "url": "https://neoflow.deno.dev/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/cz1twr07q1auzwm2huvz.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713217540/wzmu0sgztx9njkh1vfyj.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379884/portfolio/bhyfnxmqmst6a3kcxu1l.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1713217546/kwtvs3yp2ma8rqnheyqh.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379885/portfolio/aklmiy1eubzcbgccazta.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379886/portfolio/qqc4jzfxmsqsnosvdgbt.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379887/portfolio/shu1tkd7581zsjwfzoef.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379888/portfolio/wowplwdpsty9nqxjs7up.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379889/portfolio/vkmoqc28tkffz0umshhr.webp"], "md": 4}, {"logo": "3.svg", "name": "Schooler", "description": "📚 Dive into collaboration with Schooler – the ultimate document-sharing app for teachers and students! 🚀✨ Explore member management, security rules, and a sleek search feature. Enjoy the flexibility of white and dark modes. Elevate your learning journey with <PERSON><PERSON>! 💻📄", "tags": "next13 neon prisma shadcn posgresql", "url": "https://schooler-delta.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/po2jxtgbkym5quotohg2.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1721153958/odjteddjengjwyknhben.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378955/portfolio/pelxo9vzkdogdwfrzkrp.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378959/portfolio/tezacdhvx7bleewixhw7.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378955/portfolio/yftkmdhdgewfq8xolkaa.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378956/portfolio/xr4kec33dsqc7990dmwh.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378958/portfolio/npf5rtsbo2oed85yvapn.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378957/portfolio/qnhjys3ahq3xgwbyyjl9.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378958/portfolio/npf5rtsbo2oed85yvapn.webp"], "md": 3}, {"logo": "5.svg", "name": "Manga Hook", "url": "http://mangahook.vercel.app/", "description": "Dive into a world of manga magic with our free reading app! 📚🌟 Inspired by seamless design, it offers limitless creativity. Explore manga effortlessly, anytime, anywhere! 🚀", "tags": "next13 express scraping manga", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1734896921/epic/yiujh0kpxmu5pz13qkqb.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378358/portfolio/ogxaava310j3ndvk14xi.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378356/portfolio/xg9lhnzwufpwdmg3urdx.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378356/portfolio/mwvvg48whnksrnokviat.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378355/portfolio/wzdc7e75rxjwfzdy5dvd.webp"], "md": 10}, {"name": "Story Ai", "description": "✨ Introducing Story Ai: Your AI-powered creative companion! 🚀📖 Transform ideas into stories effortlessly. 🌈 Simply choose your theme, pages, and genre to get a delightful and optimistic story. 🎉 Unleash creativity with ease! ✨", "tags": "next13 react firebase stable_diffusion llama lahuggingface ai", "url": "https://story--ai.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709380342/portfolio/skkf7kxer6hfjap0su05.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709380344/portfolio/ijub3xgtnfdlxgn4kait.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709380343/portfolio/vodfhxxasznvg8m46rol.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709380967/portfolio/flsn4oza5vrzw61cpik5.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709380966/portfolio/u8znpknln32xkhkzrq0x.webp"], "md": 1}, {"name": "Manga Hook Api", "description": "📖 Manga Hook API: Your go-to documentation for fetching manga data effortlessly! 🔍🚀 Explore, search, and discover manga narratives seamlessly. #MangaHookAPI #OpenSourceLibrary", "tags": "astro documentation SSG", "url": "http://mangahook-api.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378905/portfolio/wzmsi8blha5pjjaaw1wn.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378906/portfolio/abookfnygkbavpyjdkhp.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378906/portfolio/kmh9uz5ccs8ughshdlqp.webp"], "md": 5}, {"name": "<PERSON>", "description": "🔥 Dive into Flash Read: Your go-to blog app for easy post creation, management, and community interaction! 💻📝 #FlashRead #BloggingMadeEasy", "tags": "next13 react firebase", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709382819/portfolio/zvgkqvdsjxgxg0rrxibc.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709382818/portfolio/mneznb78gzftztaondg8.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709382820/portfolio/vcl73txpdeqi6lpqi7w2.webp"], "md": 6}, {"name": "Old Portfolio", "description": "🕰️ Dive into My Old Portfolio: A snapshot of my developer journey with a landing page, stack showcase, projects, and contact section! 👨‍💻✨", "tags": "svelet tailwindcss", "url": "https://kiraazizold.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379416/portfolio/tzkoyxp4lznbvbu4eaz1.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379414/portfolio/oar39wnwglhctyud5rud.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379417/portfolio/apgpalqrqotvcusgk8vj.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709379414/portfolio/gjyhr6nxwd3yjzsg3ys1.webp"], "md": 7}, {"name": "Pixel Photos", "description": "📸 Dive into Pixel Photos: Your go-to mobile app for fetching, searching, saving, and liking pixel-perfect images! 🌈🚀 ", "tags": "next13 react firebase huggingface ai", "mobile": true, "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709386361/wqpf3ipollrkooocygsx.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709386361/kt0j5vd210stxvizlatx.webp"], "md": 8}, {"name": "Light Ai", "description": "✨ Create stunning AI images for free with Light AI! 🚀🎨 Simply input prompts and watch the magic unfold. Join the creativity at this url", "tags": "next13 react firebase huggingface ai", "url": "https://light-ai.vercel.app", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378090/portfolio/cxne4iee0mtxx3uf5ahu.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378090/portfolio/a4oftkbyijblfgog8gx1.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378089/portfolio/q8orro7mg6ddvqsvinis.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378089/portfolio/z7irpefkx7h3mbjvmola.webp"], "md": 9}, {"name": "UI editor", "description": "Revolutionize creativity with our React app! 🚀 Infused with Adobe XD vibes, it's the ultimate blend of style and functionality. Effortlessly unleash ideas! ✨", "tags": "react nextjs design ui", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378115/portfolio/seulj5hh2lgrl2ycqj7a.webp"]}, {"name": "3D editor", "description": "🚀 Dive into the magic of React 3D Editor with Three.js! This GitHub repo marries the simplicity of React with the enchanting world of Three.js. 🎨✨ Craft visually stunning 3D environments effortlessly. Your next creative adventure starts here!", "tags": "react threejs reactfiber", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378080/portfolio/xcnwte4kznhwwabqroaq.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378080/portfolio/qvnr4vtmcb8t5rw34ouu.webp"]}, {"name": "Be Shop", "description": "🛍️ Explore Be Shop: Your go-to for stylish and seamless online shopping using React, React Router, and Tailwind! 🚀💻", "tags": "react reactrouter tailwind ecommerce", "url": "https://go-beshop.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378072/portfolio/hy8hhg6ngrpgn1iyguat.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378072/portfolio/awomk9tjwdzme5mwu3ti.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709378073/portfolio/dnautltcwlee9utqlg1b.webp"]}, {"name": "Fireblog", "description": "A blog app 📝 built with Next.js, Tailwind, and PocketBase for an easy and fun experience. Find the best topics on the home page and read great articles by searching for them by tags or searching for what you want .", "tags": "react blog nextjs pocketbase", "url": "https://gofireblog.vercel.app", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377346/portfolio/eo534ncjdja1d9aude8c.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377347/portfolio/nezr6xoyzpcr8sxuiote.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377347/portfolio/jheezraxw8ziqnttkjrt.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377346/portfolio/qq13ivcymzbqgqqra4w5.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377348/portfolio/m9rxkt2ixrrfdrqcazqd.webp"]}, {"name": "My Cat", "description": "mycat 🐈❤️, desktop app that allows you to search for images on Google by typing a text in the input field, and then you can save and even download the images 💻.", "tags": "rollup svelte sqlite3 electronjs image-downloader tailwindcss", "url": "https://github.com/kiraaziz/myCat", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377300/portfolio/ilpcpbpgjwdd3usnndgd.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377301/portfolio/tvpnhv1mzai5hm9hvqve.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377301/portfolio/hyydn6nbxldvpje2m4f3.webp"]}, {"name": "Electron Book", "description": "📝 Introducing Simple Word, a Microsoft Word-like app built using React Quill 💻. Whether you're a student or a professional, Simple Word has everything you need to create and edit beautiful documents 📄", "tags": "react markdown firebase text-editor tailwindcss", "url": "https://electronbook-4c796.web.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377534/portfolio/c5safphhnsnzqkeinkas.webp"]}, {"name": "The Mountain", "description": "🏔️📸 The Mountain is a photo-sharing platform built with SvelteKit, Tailwind, and Firebase. Upload your favorite snapshots . The Mountain is the perfect place to share your adventures and discover new ones. 🌄👍👏", "tags": "sveltekit tailwind firebase photo", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377442/portfolio/b9vqryvvtqxpt0v5hzpg.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377443/portfolio/eizt5eru5sfgbv7aioxl.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377443/portfolio/xnjm4idhphmtvhxu4ytd.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377444/portfolio/ozhpp8fpfkuilreinybi.webp"]}, {"name": "MBTI say", "description": "🧐🔍 Discover the depths of your personality with our MBTI Twitter feed, built with Astro and Tailwind. Dive into the world of Myers-Briggs Type Indicator and explore an endless stream of tweets to learn more about your personality type 🔮🐦", "tags": "astro static", "url": "https://mbtisay.vercel.app/", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377451/portfolio/gbdkfvip4odvhqdcb2l6.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377451/portfolio/e8wvwqcsepp2nqk73unq.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377452/portfolio/l5uh9komr7midrswfjug.webp"]}, {"name": "Shopping Candy", "description": "A shopping app 🛒 🔥 built with Next.js, Tailwind, and PocketBase to easily shop items by tags and search with ease.", "tags": "react nextjs tailwindcss pocketbase", "url": "https://github.com/kiraaziz/shoppingCandy", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377478/portfolio/gvg6nz1yvusyckmoi5g5.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377466/portfolio/vkzfzjfyfcep9bbfum6r.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377472/portfolio/ygqiwiwyjkdvobdjq6wv.webp"]}, {"name": "My Memo", "description": "📝🔔 Keep your life organized with our React Native Memo and Reminder app. Jot down quick memos or set reminders for important events, appointments, and deadlines! ⏰✅", "tags": "react-native tailwind localstorag", "url": "https://github.com/kiraaziz/My-Memo", "mobile": true, "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377457/portfolio/gu2qdnwbujojdqwmociv.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377458/portfolio/dp0twvrljswz3m9d2bie.webp", "https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377459/portfolio/hh5tefntbzq3hltyrmqu.webp"]}, {"name": "Buzz", "description": "🎥🔊 Watch videos in style with <PERSON>, a video player built with ElectronJS and Svelte. Enjoy your favorite movies, TV shows, and videos with customizable interface. From sleek design to advanced features, <PERSON> is the perfect video player for your needs. 📺👌", "tags": "svelte tailwind electron player", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377536/portfolio/zdqeuqn2kqxuqfnhnb5e.webp"]}, {"name": "Electron Book 2.0", "description": "📝💻 Simplify your writing process with Simple Editor, a Word-like text editor built with Electron and React. Enjoy a seamless writing experience with advanced formatting . Whether you're a student, writer, or professional, Simple Editor makes it easy to create polished and professional documents. 📄✨", "tags": "electron react markdown", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377536/portfolio/wwvpwkudbae09inosf5l.webp"]}, {"name": "Simple ToDo", "description": "📌✅ Keep your life organized with Simple Todo, a minimalist to-do list app built with Svelte. Create and manage your tasks with ease . Whether you're planning your day or managing a project, Simple Todo makes it easy to stay on track. 📅👍", "url": "http://github.com/kiraaziz/ToDo", "tags": "svelte tailwind", "images": ["https://res.cloudinary.com/dqfvbunr2/image/upload/v1709377537/portfolio/kmcmgyl97yrn0t1qhqae.webp"]}]