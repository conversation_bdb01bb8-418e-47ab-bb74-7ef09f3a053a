---
import { Icon } from 'astro-icon'
const primar = [
    { name: 'Next JS', icon: 'unlogo-/logo/next-js-svgrepo-com.svg' },
    { name: 'React', icon: 'logos:react' },
    { name: 'Tailwind', icon: 'logos:tailwindcss-icon' },
    { name: 'Expo', icon: 'unlogo-/logo/expo-svgrepo-com.svg' },
    { name: 'Electron', icon: 'logos:electron' },
    { name: 'Postgresql', icon: 'logos:postgresql' },
    { name: 'Prisma', icon: 'simple-icons:prisma' },
    { name: 'Firebase', icon: 'logos:firebase' },
]
---

<div class="w-full m-auto">
    <div class="mb-3 -space-y-2">
        <h1 class="text-lg lg:text-xl font-bold">Current Tech Stack</h1>
    </div>
    <div class="w-full flex flex-wrap gap-2 h-full">
        {
            primar.map((p) => (
                <div class="flex items-center justify-center rounded-full ease-in-out duration-200 lg:hover:bg-primary/20 lg:hover:cursor-pointer"  title={p.name}>
                    <div class="h-16 w-16 border flex items-center justify-center rounded bg-muted/30 backdrop-blur">{p.icon.startsWith('unlogo') ? <img src={p.icon.split('unlogo-')[1]} class={p.icon.includes('expo') ? 'h-10' : 'h-10'} /> : <Icon name={p.icon} size="35" fill="white" />}</div>
                    <div class="hidden absolute bg-background border rounded p-2 text-sm z-10" data-popover-content>{p.name}</div>
                </div>
            ))
        }
    </div>
</div>
