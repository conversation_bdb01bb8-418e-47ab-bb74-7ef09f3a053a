---
import { epicProjects, projects } from '@/lib/data'
import ProjectDetails from '@/components/ProjectDetails'
import { Icon } from 'astro-icon'
import { useHalfText } from '@/lib/functions/useHelfText'
---

<div class="w-full">
    <div class="flex items-center justify-between w-full">
        <h1 class="text-lg lg:text-xl font-bold">Top apps</h1>
        <a class="text-sm font-light text-foreground/60 gap-2 transition-all ease-in-out duration-200 lg:hover:gap-5 flex items-center justify-center" href="/project">
            Read more
            <Icon name="lucide:arrow-right" size="20" />
        </a>
    </div>
    <div class="mt-4 grid grid-cols-1 lg:grid-cols-3 gap-2">
        {
            epicProjects.map(
                (p, index) =>
                    p.logo && (
                        <ProjectDetails
                            data={{
                                ...projects.filter((i) => {
                                    return i.name === p.name
                                })[0],
                                short: p.short
                            }}
                            isShort={true}
                            client:load />
                    ),
            )
        }
    </div>
</div>
