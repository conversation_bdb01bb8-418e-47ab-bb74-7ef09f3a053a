{"name": "growing-gravity", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/react": "^3.6.0", "@astrojs/sitemap": "^3.0.3", "@astrojs/tailwind": "^5.1.0", "@astrojs/vercel": "^6.1.0", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-slot": "^1.1.1", "@sentry/astro": "^8.19.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "astro": "^4.0.5", "astro-icon": "^0.8.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "embla-carousel-react": "^8.5.2", "lucide-react": "^0.408.0", "marked": "^12.0.0", "react": "^18.3.1", "react-activity-calendar": "^2.7.1", "react-calendly": "^4.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.5", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@iconify/react": "^5.0.1", "prettier": "^3.4.2", "prettier-plugin-astro": "^0.14.1"}}